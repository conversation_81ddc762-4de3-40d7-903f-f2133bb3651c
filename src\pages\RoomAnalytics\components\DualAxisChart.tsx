import React from 'react';
import { Line } from 'react-chartjs-2';
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  Title,
  Tooltip,
  Legend,
} from 'chart.js';
import annotationPlugin from 'chartjs-plugin-annotation';
import { TimestampedRoomData } from '@/building-mock-data-generator';

// Import utilities
import {
  filterRoomData,
  sampleDataAtSixHourIntervals,
  formatTimeLabels,
  logChartDebugInfo,
} from './chartDataProcessor';

import {
  createOccupancyZones,
  createChartData,
  createChartOptions,
} from './chartConfig';

// Register Chart.js components
ChartJS.register(
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  Title,
  Tooltip,
  Legend,
  annotationPlugin
);

interface DualAxisLineChartProps {
  data: TimestampedRoomData[];
  metricLeft: keyof TimestampedRoomData['floors'][0]['rooms'][0]['devices']['iaq'];
  metricRight: keyof TimestampedRoomData['floors'][0]['rooms'][0]['devices']['iaq'];
  roomId: string;
  dateRange: { startDate: Date; endDate: Date };
}

// Occupancy Legend Component
const OccupancyLegend: React.FC = () => {
  const categories = [
    { label: 'Unoccupied', color: '#F9C36A' }, // Yellow/Amber
    { label: 'Occupied', color: '#59C9C4' }, // Teal/Green
    { label: 'Error', color: '#FF5F4A' }, // Red/Orange
  ];

  return (
    <div className="mt-2 flex flex-wrap gap-4 text-xs justify-center">
      {categories.map((category, index) => (
        <div key={index} className="flex items-center gap-1">
          <div
            className="w-3 h-3 rounded"
            style={{ backgroundColor: category.color }}
          />
          <span>{category.label}</span>
        </div>
      ))}
    </div>
  );
};

// IoT Summary Component
interface IoTSummaryProps {
  data: TimestampedRoomData[];
  roomId: string;
  dateRange: { startDate: Date; endDate: Date };
}

const IoTSummary: React.FC<IoTSummaryProps> = ({ data, roomId, dateRange }) => {
  // Calculate statistics for all IoT metrics
  const calculateStats = () => {
    const roomData = data
      .filter(
        (entry) =>
          new Date(entry.timestamp) >= dateRange.startDate &&
          new Date(entry.timestamp) <= dateRange.endDate
      )
      .map((entry) => {
        const room = entry.floors
          .flatMap((f) => f.rooms)
          .find((r) => r.roomId === roomId);
        return room?.devices.iaq;
      })
      .filter(Boolean);

    if (roomData.length === 0) return null;

    const metrics = ['temperature', 'humidity', 'illuminance', 'noise', 'pm25', 'co2'] as const;
    const stats: Record<string, { avg: number; max: number; min: number }> = {};

    metrics.forEach((metric) => {
      const values = roomData
        .map((data) => data![metric])
        .filter((val): val is number => typeof val === 'number');

      if (values.length > 0) {
        stats[metric] = {
          avg: values.reduce((sum, val) => sum + val, 0) / values.length,
          max: Math.max(...values),
          min: Math.min(...values),
        };
      }
    });

    return stats;
  };

  const stats = calculateStats();

  if (!stats) {
    return (
      <div className="mt-4 p-4 bg-gray-50 rounded-lg">
        <div style={{ color: '#065BA9', fontSize: 10, fontFamily: 'Inter', fontWeight: '500', lineHeight: '12px' }}>
          IoT Summary
        </div>
        <div className="mt-2 text-gray-500 text-xs">No data available for the selected period</div>
      </div>
    );
  }

  const formatValue = (metric: string, value: number) => {
    switch (metric) {
      case 'temperature':
        return `${value.toFixed(1)}°C`;
      case 'humidity':
        return `${value.toFixed(0)}%`;
      case 'illuminance':
        return `${value.toFixed(0)} lux`;
      case 'noise':
        return `${value.toFixed(0)} dB`;
      case 'pm25':
        return `${value.toFixed(1)} µg/m³`;
      case 'co2':
        return `${value.toFixed(0)} ppm`;
      default:
        return value.toFixed(1);
    }
  };

  const getMetricDisplayName = (metric: string) => {
    switch (metric) {
      case 'pm25':
        return 'PM2.5';
      case 'co2':
        return 'CO2';
      default:
        return metric.charAt(0).toUpperCase() + metric.slice(1);
    }
  };

  return (
    <div className="mt-4 p-4 bg-white border rounded-lg">
      <div style={{ color: '#065BA9', fontSize: 10, fontFamily: 'Inter', fontWeight: '500', lineHeight: '12px', marginBottom: '12px' }}>
        IoT Summary
      </div>

      <div className="grid grid-cols-4 gap-2">
        {/* Header Row */}
        <div style={{ color: '#9CA3AF', fontSize: 10, fontFamily: 'Inter', fontWeight: '500', lineHeight: '12px' }}>
          Parameter
        </div>
        <div style={{ color: '#9CA3AF', fontSize: 10, fontFamily: 'Inter', fontWeight: '500', lineHeight: '12px' }}>
          Avg
        </div>
        <div style={{ color: '#9CA3AF', fontSize: 10, fontFamily: 'Inter', fontWeight: '500', lineHeight: '12px' }}>
          Max
        </div>
        <div style={{ color: '#9CA3AF', fontSize: 10, fontFamily: 'Inter', fontWeight: '500', lineHeight: '12px' }}>
          Min
        </div>

        {/* Data Rows */}
        {Object.entries(stats).map(([metric, values]) => (
          <React.Fragment key={metric}>
            <div style={{ color: '#374151', fontSize: 10, fontFamily: 'Inter', fontWeight: '500', lineHeight: '12px' }}>
              {getMetricDisplayName(metric)}
            </div>
            <div style={{ color: '#374151', fontSize: 10, fontFamily: 'Inter', fontWeight: '500', lineHeight: '12px' }}>
              {formatValue(metric, values.avg)}
            </div>
            <div style={{ color: '#374151', fontSize: 10, fontFamily: 'Inter', fontWeight: '500', lineHeight: '12px' }}>
              {formatValue(metric, values.max)}
            </div>
            <div style={{ color: '#374151', fontSize: 10, fontFamily: 'Inter', fontWeight: '500', lineHeight: '12px' }}>
              {formatValue(metric, values.min)}
            </div>
          </React.Fragment>
        ))}
      </div>
    </div>
  );
};

// No Data Message Component
const NoDataMessage: React.FC<{ roomId: string; dateRange: { startDate: Date; endDate: Date } }> = ({
  roomId,
  dateRange
}) => {
  return (
    <div className="h-64 flex items-center justify-center border rounded-lg bg-gray-50">
      <div className="text-center text-gray-500">
        <p className="text-lg font-medium">No data available</p>
        <p className="text-sm">
          No data found for Room {roomId} in the selected date range.
        </p>
        <p className="text-xs mt-2">
          Date range: {dateRange.startDate.toLocaleDateString()} -{' '}
          {dateRange.endDate.toLocaleDateString()}
        </p>
      </div>
    </div>
  );
};

export const DualAxisLineChart: React.FC<DualAxisLineChartProps> = ({
  data,
  metricLeft,
  metricRight,
  roomId,
  dateRange,
}) => {
  // Process data using utility functions
  const allFilteredData = filterRoomData(data, roomId, dateRange, metricLeft, metricRight);
  const filteredData = sampleDataAtSixHourIntervals(allFilteredData, dateRange);

  // Debug logging
  logChartDebugInfo(
    roomId,
    data.length,
    allFilteredData.length,
    filteredData.length,
    dateRange,
    filteredData
  );

  // Generate chart elements using utility functions
  const labels = formatTimeLabels(filteredData);
  const occupancyZones = createOccupancyZones(filteredData);

  // Create chart configuration using utility functions
  const chartData = createChartData(labels, filteredData, metricLeft, metricRight);
  const options = createChartOptions(roomId, metricLeft, metricRight, occupancyZones);

  // Show no data message if no data is available
  if (filteredData.length === 0) {
    return <NoDataMessage roomId={roomId} dateRange={dateRange} />;
  }

  return (
    <div className="w-full h-full">
      <Line data={chartData} options={options} />
      <OccupancyLegend />
    </div>
  );
};

import React from 'react';
import { Line } from 'react-chartjs-2';
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  Title,
  Tooltip,
  Legend,
} from 'chart.js';
import annotationPlugin from 'chartjs-plugin-annotation';
import { TimestampedRoomData } from '@/building-mock-data-generator';

// Import utilities
import {
  filterRoomData,
  sampleDataAtSixHourIntervals,
  formatTimeLabels,
  logChartDebugInfo,
} from './chartDataProcessor';

import {
  createOccupancyZones,
  createChartData,
  createChartOptions,
} from './chartConfig';

// Register Chart.js components
ChartJS.register(
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  Title,
  Tooltip,
  Legend,
  annotationPlugin
);

interface DualAxisLineChartProps {
  data: TimestampedRoomData[];
  metricLeft: keyof TimestampedRoomData['floors'][0]['rooms'][0]['devices']['iaq'];
  metricRight: keyof TimestampedRoomData['floors'][0]['rooms'][0]['devices']['iaq'];
  roomId: string;
  dateRange: { startDate: Date; endDate: Date };
}

// Occupancy Legend Component
const OccupancyLegend: React.FC = () => {
  const categories = [
    { label: 'Unoccupied', color: 'rgba(34, 197, 94, 0.4)' },
    { label: 'Occupied', color: 'rgba(251, 191, 36, 0.4)' },
    { label: 'Error', color: 'rgba(239, 68, 68, 0.4)' },
  ];

  return (
    <div className="mt-2 flex flex-wrap gap-4 text-xs justify-center">
      {categories.map((category, index) => (
        <div key={index} className="flex items-center gap-1">
          <div
            className="w-3 h-3 rounded"
            style={{ backgroundColor: category.color }}
          />
          <span>{category.label}</span>
        </div>
      ))}
    </div>
  );
};

// No Data Message Component
const NoDataMessage: React.FC<{ roomId: string; dateRange: { startDate: Date; endDate: Date } }> = ({
  roomId,
  dateRange
}) => {
  return (
    <div className="h-64 flex items-center justify-center border rounded-lg bg-gray-50">
      <div className="text-center text-gray-500">
        <p className="text-lg font-medium">No data available</p>
        <p className="text-sm">
          No data found for Room {roomId} in the selected date range.
        </p>
        <p className="text-xs mt-2">
          Date range: {dateRange.startDate.toLocaleDateString()} -{' '}
          {dateRange.endDate.toLocaleDateString()}
        </p>
      </div>
    </div>
  );
};

export const DualAxisLineChart: React.FC<DualAxisLineChartProps> = ({
  data,
  metricLeft,
  metricRight,
  roomId,
  dateRange,
}) => {
  // Process data using utility functions
  const allFilteredData = filterRoomData(data, roomId, dateRange, metricLeft, metricRight);
  const filteredData = sampleDataAtSixHourIntervals(allFilteredData, dateRange);

  // Debug logging
  logChartDebugInfo(
    roomId,
    data.length,
    allFilteredData.length,
    filteredData.length,
    dateRange,
    filteredData
  );

  // Generate chart elements using utility functions
  const labels = formatTimeLabels(filteredData);
  const occupancyZones = createOccupancyZones(filteredData);

  // Create chart configuration using utility functions
  const chartData = createChartData(labels, filteredData, metricLeft, metricRight);
  const options = createChartOptions(roomId, metricLeft, metricRight, occupancyZones);

  // Show no data message if no data is available
  if (filteredData.length === 0) {
    return <NoDataMessage roomId={roomId} dateRange={dateRange} />;
  }

  return (
    <div className="w-full h-full">
      <Line data={chartData} options={options} />
      <OccupancyLegend />
    </div>
  );
};
